import React, { useState, useEffect, useCallback, useRef } from "react";
import { Container, Box, Paper, Typography, Divider } from "@mui/material";
import PDFViewer from "../components/PDFViewer";
import AudioControls from "../components/AudioControls";
import Header from "../components/Header";
import Footer from "../components/Footer";
import { usePDFDocument } from "../hooks/usePDFDocument";
import { useSpeechSynthesis } from "../hooks/useSpeechSynthesis";
import type { TextItem, WordItem } from "../types";
import { extractTextFromPage } from "../utils/pdfHelpers";
import {
  findWordItemAtPosition,
  findReadingStartPosition,
  determineBoundaryType,
  getWordIndexFromCharIndex
} from "../utils/textBoundaryHelpers";
import WordHighlightOverlay from "../components/WordHighlightOverlay";
import "../styles/highlighting.css";

const HomePage: React.FC = () => {
  const {
    documentInfo,
    currentPageText,
    currentPageWordItems,
    highlights,
    loadDocument,
    setCurrentPage,
    addHighlight,
    clearHighlights,
    isLoading,
  } = usePDFDocument();

  const {
    voices,
    options,
    speaking,
    paused,
    speak,
    pause,
    resume,
    stop,
    setOptions,
    speakFromPosition,
    getCurrentWordInfo,
  } = useSpeechSynthesis();

  const [currentHighlightedText, setCurrentHighlightedText] = useState("");
  const [currentHighlight, setCurrentHighlight] = useState<TextItem | null>(
    null
  );
  const [spreadTextItems, setSpreadTextItems] = useState<TextItem[]>([]);
  const [isAutoReading, setIsAutoReading] = useState(false);
  const [readingProgress, setReadingProgress] = useState(0);
  const [currentWordIndex, setCurrentWordIndex] = useState<number | null>(null);
  const [allWordItems, setAllWordItems] = useState<WordItem[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  // Utility function to detect selection type and expand if needed
  const expandSelection = useCallback((selectedText: string, fullText: string): { text: string; startPosition: number } => {
    const trimmedSelection = selectedText.trim();

    // If selection is very short (1-2 words), try to expand to sentence
    const words = trimmedSelection.split(/\s+/);
    if (words.length <= 2) {
      // Find the sentence containing this selection
      const sentences = fullText.split(/[.!?]+/);
      for (const sentence of sentences) {
        if (sentence.toLowerCase().includes(trimmedSelection.toLowerCase())) {
          const sentenceStart = fullText.toLowerCase().indexOf(sentence.toLowerCase());
          if (sentenceStart !== -1) {
            return {
              text: sentence.trim(),
              startPosition: sentenceStart
            };
          }
        }
      }
    }

    // For longer selections, use as-is but find accurate position
    const startPosition = fullText.toLowerCase().indexOf(trimmedSelection.toLowerCase());
    return {
      text: trimmedSelection,
      startPosition: startPosition !== -1 ? startPosition : 0
    };
  }, []);

  useEffect(() => {
    loadDocument("/src/assets/sample.pdf");
  }, [loadDocument]);

  const handleTextSelection = useCallback(async (text: string, pageIndex: number) => {
    if (text && documentInfo) {
      if (speaking) {
        stop();
      }

      // Get the current page text and word items
      const { text: fullText, wordItems } = await getSpreadTextAndItems(documentInfo.currentPage);

      // Determine the boundary type based on selection
      const boundaryType = determineBoundaryType(text);

      // Find the starting position in the full text
      const startPosition = fullText.toLowerCase().indexOf(text.toLowerCase());

      if (startPosition !== -1) {
        // Find the word item at this position
        const wordItem = findWordItemAtPosition(wordItems, startPosition);

        if (wordItem) {
          // Get the optimal reading start position based on boundary type
          const readingStartPosition = findReadingStartPosition(fullText, wordItems, wordItem, boundaryType);

          setCurrentHighlightedText(text);
          addHighlight({ pageIndex, text });

          // Start reading from the optimal position
          speakFromPosition(
            fullText,
            readingStartPosition,
            pageIndex,
            (event) => {
              if (event.charIndex !== undefined && event.charLength !== undefined) {
                const charIndex = readingStartPosition + event.charIndex;
                const currentWordItem = findWordItemAtPosition(wordItems, charIndex);
                const wordIndex = currentWordItem ? getWordIndexFromCharIndex(wordItems, charIndex) : null;
                setCurrentWordIndex(wordIndex);
              }
            },
            () => {
              setCurrentWordIndex(null);
              setCurrentHighlight(null);
            }
          );
        } else {
          // Fallback to original behavior
          setCurrentHighlightedText(text);
          addHighlight({ pageIndex, text });
          speak(text, [], pageIndex);
        }
      } else {
        // Fallback to original behavior
        setCurrentHighlightedText(text);
        addHighlight({ pageIndex, text });
        speak(text, [], pageIndex);
      }
    }
  }, [documentInfo, speaking, stop, determineBoundaryType, findWordItemAtPosition, findReadingStartPosition, getWordIndexFromCharIndex, speakFromPosition, addHighlight, speak]);

  // Enhanced handler for word click from PDFViewer
  const handleWordClick = useCallback(async (word: string, pageIndex: number) => {
    if (!documentInfo) return;
    if (speaking) {
      stop();
    }

    // Get the current page text and word items
    const { text, wordItems } = await getSpreadTextAndItems(documentInfo.currentPage);

    // Find the clicked word in the word items
    const clickedWordItem = wordItems.find(item =>
      item.word.toLowerCase() === word.toLowerCase() &&
      item.pageIndex === pageIndex
    );

    if (clickedWordItem && text) {
      // Determine boundary type (for now, start from the word)
      const boundaryType = determineBoundaryType(word);
      const startPosition = findReadingStartPosition(text, wordItems, clickedWordItem, boundaryType);

      // Start reading from the clicked position
      speakFromPosition(
        text,
        startPosition,
        pageIndex,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = startPosition + event.charIndex;
            const wordItem = findWordItemAtPosition(wordItems, charIndex);
            const wordIndex = wordItem ? getWordIndexFromCharIndex(wordItems, charIndex) : null;
            setCurrentWordIndex(wordIndex);
          }
        },
        () => {
          setCurrentWordIndex(null);
          setCurrentHighlight(null);
        }
      );
    } else {
      // Fallback: use the old method
      const findWordPosition = (text: string, targetWord: string): number => {
        const words = text.split(/\s+/);
        let charIndex = 0;

        for (let i = 0; i < words.length; i++) {
          const currentWord = words[i].replace(/[^\w]/g, '').toLowerCase();
          const target = targetWord.replace(/[^\w]/g, '').toLowerCase();

          if (currentWord === target) {
            return charIndex;
          }
          charIndex += words[i].length + 1;
        }

        return text.toLowerCase().indexOf(targetWord.toLowerCase());
      };

      const startPosition = findWordPosition(currentHighlightedText, word);
      if (startPosition !== -1) {
        speakFromPosition(currentHighlightedText, startPosition, pageIndex);
      } else {
        speak(currentHighlightedText, [], pageIndex);
      }
    }
  }, [documentInfo, speaking, stop, determineBoundaryType, findReadingStartPosition, speakFromPosition, findWordItemAtPosition, getWordIndexFromCharIndex, currentHighlightedText, speak]);

  // Enhanced handler for pointer over word from PDFViewer
  const handleWordPointerOver = useCallback((() => {
    let timeoutId: NodeJS.Timeout | null = null;
    return (word: string, pageIndex: number) => {
      if (!documentInfo) return;
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        if (speaking) {
          stop();
        }

        // Find the word position more accurately
        const findWordPosition = (text: string, targetWord: string): number => {
          const words = text.split(/\s+/);
          let charIndex = 0;

          for (let i = 0; i < words.length; i++) {
            const currentWord = words[i].replace(/[^\w]/g, '').toLowerCase();
            const target = targetWord.replace(/[^\w]/g, '').toLowerCase();

            if (currentWord === target) {
              return charIndex;
            }
            charIndex += words[i].length + 1;
          }

          return text.toLowerCase().indexOf(targetWord.toLowerCase());
        };

        const startPosition = findWordPosition(currentHighlightedText, word);
        if (startPosition !== -1) {
          speakFromPosition(currentHighlightedText, startPosition, pageIndex);
        } else {
          speak(currentHighlightedText, [], pageIndex);
        }
      }, 300);
    };
  })(), [documentInfo, speaking, stop, currentHighlightedText, speakFromPosition, speak]);

  // Enhanced handler for text selection change to start reading from selection on mouseup
  const handleMouseUp = useCallback(async () => {
    if (!documentInfo) return;
    const selection = window.getSelection();
    if (selection && selection.toString()) {
      if (speaking) {
        stop();
      }
      const selectedText = selection.toString().trim();

      // Get the current page text and word items
      const { text: fullText, wordItems } = await getSpreadTextAndItems(documentInfo.currentPage);

      // Determine the boundary type based on selection
      const boundaryType = determineBoundaryType(selectedText);

      // Find the starting position in the full text
      const startPosition = fullText.toLowerCase().indexOf(selectedText.toLowerCase());

      if (startPosition !== -1) {
        // Find the word item at this position
        const wordItem = findWordItemAtPosition(wordItems, startPosition);

        if (wordItem) {
          // Get the optimal reading start position based on boundary type
          const readingStartPosition = findReadingStartPosition(fullText, wordItems, wordItem, boundaryType);

          // Add highlight for visual feedback
          addHighlight({ pageIndex: documentInfo.currentPage - 1, text: selectedText });

          // Start reading from the optimal position
          speakFromPosition(
            fullText,
            readingStartPosition,
            documentInfo.currentPage - 1,
            (event) => {
              if (event.charIndex !== undefined && event.charLength !== undefined) {
                const charIndex = readingStartPosition + event.charIndex;
                const currentWordItem = findWordItemAtPosition(wordItems, charIndex);
                const wordIndex = currentWordItem ? getWordIndexFromCharIndex(wordItems, charIndex) : null;
                setCurrentWordIndex(wordIndex);
              }
            },
            () => {
              setCurrentWordIndex(null);
              setCurrentHighlight(null);
            }
          );
        } else {
          // Fallback to expandSelection utility
          const { text: textToRead, startPosition: expandedStartPosition } = expandSelection(selectedText, currentHighlightedText);
          addHighlight({ pageIndex: documentInfo.currentPage - 1, text: selectedText });

          if (expandedStartPosition !== -1) {
            speakFromPosition(currentHighlightedText, expandedStartPosition, documentInfo.currentPage - 1);
          } else {
            speak(textToRead, [], documentInfo.currentPage - 1);
          }
        }
      } else {
        // Fallback to expandSelection utility
        const { text: textToRead, startPosition: expandedStartPosition } = expandSelection(selectedText, currentHighlightedText);
        addHighlight({ pageIndex: documentInfo.currentPage - 1, text: selectedText });

        if (expandedStartPosition !== -1) {
          speakFromPosition(currentHighlightedText, expandedStartPosition, documentInfo.currentPage - 1);
        } else {
          speak(textToRead, [], documentInfo.currentPage - 1);
        }
      }
    }
  }, [documentInfo, speaking, stop, determineBoundaryType, findWordItemAtPosition, findReadingStartPosition, getWordIndexFromCharIndex, speakFromPosition, addHighlight, speak, expandSelection, currentHighlightedText]);

  // Replace selectionchange event with mouseup event listener
  React.useEffect(() => {
    document.addEventListener("mouseup", handleMouseUp);
    return () => {
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [speaking, currentHighlightedText, documentInfo]);

  const getSpreadTextAndItems = useCallback(
    async (pageNumber: number) => {
      if (!documentInfo || !documentInfo.pdfDoc)
        return { text: "", textItems: [], wordItems: [] };

      let text = "";
      let textItems: TextItem[] = [];
      let wordItems: WordItem[] = [];

      if (pageNumber === 1) {
        const { fullText, textItems: pageTextItems, wordItems: pageWordItems } =
          await extractTextFromPage(documentInfo.pdfDoc, 1);
        text = fullText;
        textItems = pageTextItems;
        wordItems = pageWordItems;
      } else {
        const { fullText: leftText, textItems: leftTextItems, wordItems: leftWordItems } =
          await extractTextFromPage(documentInfo.pdfDoc, pageNumber);
        const rightPage =
          pageNumber + 1 <= documentInfo.totalPages ? pageNumber + 1 : null;
        let rightText = "";
        let rightTextItems: TextItem[] = [];
        let rightWordItems: WordItem[] = [];
        if (rightPage) {
          const { fullText, textItems, wordItems: pageWordItems } = await extractTextFromPage(
            documentInfo.pdfDoc,
            rightPage
          );
          rightText = fullText;
          rightTextItems = textItems.map((item) => ({
            ...item,
            startIndex: item.startIndex + leftText.length + 1,
            endIndex: item.endIndex + leftText.length + 1,
          }));
          rightWordItems = pageWordItems.map((item) => ({
            ...item,
            startIndex: item.startIndex + leftText.length + 1,
            endIndex: item.endIndex + leftText.length + 1,
          }));
        }
        text = `${leftText} ${rightText}`.trim();
        textItems = [...leftTextItems, ...rightTextItems];
        wordItems = [...leftWordItems, ...rightWordItems];
      }

      return { text, textItems, wordItems };
    },
    [documentInfo]
  );

  const handleReadingComplete = useCallback(async () => {
    if (!documentInfo || !isAutoReading) return;

    const currentPage = documentInfo.currentPage;
    let nextPage;

    if (currentPage % 2 === 1) {
      nextPage = currentPage + 1;
    } else {
      nextPage = currentPage + 1;
    }

    if (nextPage > documentInfo.totalPages) {
      setIsAutoReading(false);
      stop();
      clearHighlights();
      setCurrentHighlight(null);
    } else {
      stop();
      await setCurrentPage(nextPage);
      setTimeout(() => {
        if (isAutoReading) {
          setIsAutoReading(true);
        }
      }, 500);
    }
  }, [documentInfo, isAutoReading, setCurrentPage, stop, clearHighlights]);

  useEffect(() => {
    if (
      !isAutoReading ||
      !documentInfo ||
      speaking ||
      isLoading ||
      !documentInfo.pdfDoc
    )
      return;

    const readingTimeout = setTimeout(async () => {
      const { text, textItems, wordItems } = await getSpreadTextAndItems(
        documentInfo.currentPage
      );
      setSpreadTextItems(textItems);
      setAllWordItems(wordItems);
      setCurrentHighlightedText(text);
      speak(
        text,
        [],
        documentInfo.currentPage - 1,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;

            // Find the word item that contains the current character
            const wordItem = findWordItemAtPosition(wordItems, charIndex);
            const wordIndex = wordItem ? getWordIndexFromCharIndex(wordItems, charIndex) : null;

            // Update current word index for highlighting
            setCurrentWordIndex(wordIndex);

            // Find the text item that contains the current character
            const textItem = textItems.find(
              (item) =>
                charIndex >= item.startIndex && charIndex < item.endIndex
            );

            if (textItem) {
              // Get the actual word being spoken with improved detection
              const word = text.substring(
                charIndex,
                charIndex + event.charLength
              ).trim();

              // Calculate more precise word boundaries
              const wordStart = Math.max(charIndex, textItem.startIndex);
              const wordEnd = Math.min(
                charIndex + event.charLength,
                textItem.endIndex
              );

              // Create enhanced highlight item with better positioning
              const highlightItem = {
                ...textItem,
                text: word,
                startIndex: wordStart,
                endIndex: wordEnd,
                coordinates: {
                  ...textItem.coordinates,
                  x: 0, // Start from the left edge for full-width highlighting
                  width: containerRef.current?.offsetWidth || window.innerWidth,
                  height: Math.max(textItem.coordinates.height, 20), // Ensure minimum height
                },
              };

              // Update highlight with smooth transition
              setCurrentHighlight(highlightItem);

              // Update reading progress
              const progress = (charIndex / text.length) * 100;
              setReadingProgress(Math.min(progress, 100));

              // Add word-level highlighting animation
              if (containerRef.current) {
                const highlightElements = containerRef.current.querySelectorAll('.word-highlight');
                highlightElements.forEach(el => el.classList.add('highlight-pulse'));
                setTimeout(() => {
                  highlightElements.forEach(el => el.classList.remove('highlight-pulse'));
                }, 200);
              }
            }
          }
        },
        () => {
          setCurrentHighlight(null);
          handleReadingComplete();
        }
      );
    }, 300);

    return () => clearTimeout(readingTimeout);
  }, [
    isAutoReading,
    documentInfo,
    speaking,
    isLoading,
    getSpreadTextAndItems,
    speak,
    handleReadingComplete,
  ]);

  return (
    <Box className="min-h-screen flex flex-col bg-gray-100">
      {/* Reading Progress Indicator */}
      {(speaking || isAutoReading) && (
        <Box
          className="reading-progress"
          sx={{
            width: `${readingProgress}%`,
            position: 'fixed',
            top: 0,
            left: 0,
            height: '3px',
            background: 'linear-gradient(90deg, #FF5722, #FF9800, #FFC107)',
            transition: 'width 0.1s ease',
            zIndex: 1000,
            boxShadow: '0 0 10px rgba(255, 87, 34, 0.5)',
          }}
        />
      )}
      <Header onHistoryClick={() => {}} />
      <Container
        maxWidth="xl"
        sx={{ flexGrow: 1, py: 4, display: "flex", flexDirection: "column" }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", md: "row" },
            gap: 3,
            height: "100%",
          }}
        >
          <Box
            sx={{
              width: { xs: "100%", md: "30%" },
              display: "flex",
              flexDirection: "column",
            }}
          >
            <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
              <Typography
                variant="h4"
                component="h1"
                gutterBottom
                align="center"
                fontWeight="bold"
              >
                TSLS READS
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ position: 'relative' }}>
                <AudioControls
                  playing={speaking}
                  paused={paused}
                  onPlay={() => setIsAutoReading(true)}
                  onPause={pause}
                  onResume={resume}
                  onStop={() => {
                    stop();
                    setIsAutoReading(false);
                    clearHighlights();
                    setCurrentHighlight(null);
                    setReadingProgress(0);
                  }}
                  onNext={async () => {
                    if (!documentInfo) return;
                    stop();
                    setIsAutoReading(false);
                    clearHighlights();
                    setCurrentHighlight(null);
                    setReadingProgress(0);
                    const nextPage = documentInfo.currentPage + 1;
                    if (nextPage <= documentInfo.totalPages) {
                      await setCurrentPage(nextPage);
                    }
                  }}
                  onPrevious={async () => {
                    if (!documentInfo) return;
                    stop();
                    setIsAutoReading(false);
                    clearHighlights();
                    setCurrentHighlight(null);
                    setReadingProgress(0);
                    const prevPage = documentInfo.currentPage - 1;
                    if (prevPage >= 1) {
                      await setCurrentPage(prevPage);
                    }
                  }}
                  voiceOptions={options}
                  setVoiceOptions={setOptions}
                  availableVoices={voices}
                />

                {/* Voice Activity Indicator */}
                {speaking && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: -10,
                      right: -10,
                      width: 20,
                      height: 20,
                      borderRadius: '50%',
                      background: 'linear-gradient(45deg, #FF5722, #FF9800)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '10px',
                      animation: 'voicePulse 1s ease-in-out infinite',
                      boxShadow: '0 0 10px rgba(255, 87, 34, 0.5)',
                      zIndex: 10,
                    }}
                  >
                    🔊
                  </Box>
                )}
              </Box>
            </Paper>

            {/* Reading Status Indicator */}
            {(speaking || isAutoReading) && (
              <Paper
                elevation={2}
                sx={{
                  p: 2,
                  mt: 2,
                  background: 'linear-gradient(135deg, #E3F2FD, #F3E5F5)',
                  border: '1px solid #E1BEE7',
                }}
              >
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                  📖 Currently Reading
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary', mb: 1 }}>
                  Page {documentInfo?.currentPage} of {documentInfo?.totalPages}
                </Typography>
                {currentHighlight && (
                  <Typography
                    variant="body2"
                    sx={{
                      fontStyle: 'italic',
                      background: 'rgba(255, 235, 59, 0.3)',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      display: 'inline-block',
                    }}
                  >
                    "{currentHighlight.text}"
                  </Typography>
                )}
                <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="caption">Progress:</Typography>
                  <Box
                    sx={{
                      flexGrow: 1,
                      height: 4,
                      background: '#E0E0E0',
                      borderRadius: 2,
                      overflow: 'hidden',
                    }}
                  >
                    <Box
                      sx={{
                        width: `${readingProgress}%`,
                        height: '100%',
                        background: 'linear-gradient(90deg, #FF5722, #FF9800)',
                        transition: 'width 0.1s ease',
                      }}
                    />
                  </Box>
                  <Typography variant="caption">
                    {Math.round(readingProgress)}%
                  </Typography>
                </Box>
              </Paper>
            )}
            <Paper
              elevation={3}
              sx={{ p: 3, flexGrow: 1, display: { xs: "none", md: "block" } }}
            >
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 800,
                  textAlign: "center",
                }}
              >
                CONTACT INFORMATION
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ mt: 2 }}>
                <Typography>Email: <EMAIL></Typography>
                <Typography>Phone: (*************</Typography>
                <Typography>Address: 123 Main St, Anytown, USA</Typography>
                <Typography>Website: www.example.com</Typography>
                <Typography>Opening Hours: 9am-5pm, Monday-Friday</Typography>
                <Typography>Social Media: @example</Typography>
              </Box>
            </Paper>
          </Box>

          <Box sx={{ width: { xs: "100%" }, flexGrow: 1 }} ref={containerRef}>
            <Paper
              elevation={3}
              sx={{
                height: "100%",
                p: 2,
                display: "flex",
                flexDirection: "column",
                overflow: "hidden",
              }}
            >
              <Box sx={{ position: 'relative', height: '100%' }}>
                <PDFViewer
                  document={documentInfo}
                  highlights={highlights}
                  onPageChange={setCurrentPage}
                  onTextSelection={handleTextSelection}
                  onWordClick={handleWordClick}
                  onWordPointerOver={handleWordPointerOver}
                  currentHighlight={currentHighlight}
                  pageHeight={documentInfo?.pageHeight || 0}
                  isReading={isAutoReading || speaking}
                />
                {/* Word Highlight Overlay */}
                {documentInfo && allWordItems.length > 0 && (
                  <WordHighlightOverlay
                    wordItems={allWordItems}
                    currentWordIndex={currentWordIndex}
                    pageNumber={documentInfo.currentPage}
                    scale={1.2} // Match the PDF scale
                    pageHeight={documentInfo.pageHeight || 0}
                    isReading={isAutoReading || speaking}
                    onWordClick={async (_wordIndex: number, word: string) => {
                      if (!speaking) {
                        await handleWordClick(word, documentInfo.currentPage - 1);
                      }
                    }}
                  />
                )}
              </Box>
            </Paper>
          </Box>
        </Box>
      </Container>
      <Footer />
    </Box>
  );
};

export default HomePage;
