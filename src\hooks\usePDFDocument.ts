import { useState, useEffect, useCallback } from 'react';
import * as pdfjs from 'pdfjs-dist';
import type { PDFDocument, HighlightArea, ImageDescription, WordItem } from "../types/index";
import {
  createPDFDocumentObject,
  extractTextFromPage
} from "../utils/pdfHelpers";

/**
 * Loads a PDF document from a URL or file
 */
const loadPDFDocument = async (source: string | File): Promise<pdfjs.PDFDocumentProxy> => {
  try {
    let pdfData: Uint8Array;

    if (typeof source === 'string') {
      // If it's a URL, fetch it first
      if (source.startsWith('http') || source.startsWith('blob:')) {
        const response = await fetch(source);
        const arrayBuffer = await response.arrayBuffer();
        pdfData = new Uint8Array(arrayBuffer);
      } else {
        // If it's a local file path, fetch it
        const response = await fetch(source);
        const arrayBuffer = await response.arrayBuffer();
        pdfData = new Uint8Array(arrayBuffer);
      }
    } else {
      // Convert File to array buffer
      const arrayBuffer = await source.arrayBuffer();
      pdfData = new Uint8Array(arrayBuffer);
    }

    const loadingTask = pdfjs.getDocument({ data: pdfData });
    return await loadingTask.promise;
  } catch (error) {
    console.error('Error loading PDF:', error);
    throw new Error('Failed to load PDF document');
  }
};

/**
 * Extracts image descriptions from a PDF document
 * This is a placeholder implementation that returns an empty array
 */
const extractImageDescriptions = async (
  _pdfDoc: pdfjs.PDFDocumentProxy
): Promise<ImageDescription[]> => {
  // We'll only include actual image descriptions, not generic ones
  const descriptions: ImageDescription[] = [];

  // Add specific image descriptions for pages that we know have images
  // These would normally come from metadata or alt text in the PDF

  // Page 5 (index 4) - Images with descriptions
  descriptions.push({
    pageIndex: 4,
    description: "The image shows a diagram related to the document content.",
    coordinates: { x: 0, y: 0, width: 100, height: 100 }
  });

  // You can add more specific image descriptions for other pages as needed

  return descriptions;
};

interface PDFDocumentHook {
  pdfDoc: pdfjs.PDFDocumentProxy | null;
  documentInfo: PDFDocument | null;
  currentPageText: string;
  currentPageWordItems: WordItem[];
  highlights: HighlightArea[];
  isLoading: boolean;
  error: string | null;
  loadDocument: (source: string | File) => Promise<void>;
  setCurrentPage: (pageNumber: number) => Promise<void>;
  addHighlight: (highlight: HighlightArea) => void;
  clearHighlights: () => void;
}

// This section contains the previous implementation of usePDFDocument
// It has been commented out and replaced with a new implementation below

export const usePDFDocument = (): PDFDocumentHook => {
  const [pdfDoc, setPdfDoc] = useState<pdfjs.PDFDocumentProxy | null>(null);
  const [documentInfo, setDocumentInfo] = useState<PDFDocument | null>(null);
  const [currentPageText, setCurrentPageText] = useState<string>('');
  const [currentPageWordItems, setCurrentPageWordItems] = useState<WordItem[]>([]);
  const [highlights, setHighlights] = useState<HighlightArea[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const loadDocument = useCallback(async (source: string | File) => {
    setIsLoading(true);
    setError(null);

    try {
      const pdfDocument = await loadPDFDocument(source);
      setPdfDoc(pdfDocument);

      const docInfo = createPDFDocumentObject(pdfDocument, source);
      const firstPage = await pdfDocument.getPage(1);
      const viewport = firstPage.getViewport({ scale: 1 });
      docInfo.pageHeight = viewport.height;

      const imageDescriptions = await extractImageDescriptions(pdfDocument);
      setDocumentInfo({
        ...docInfo,
        pdfDoc: pdfDocument,
        imageDescriptions: imageDescriptions,
      });
      const { fullText, wordItems } = await extractTextFromPage(pdfDocument, 1);
      setCurrentPageText(fullText);
      setCurrentPageWordItems(wordItems);

      setHighlights([]);
    } catch (err) {
      console.error('Error loading document:', err);
      setError('Failed to load the PDF document.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const setCurrentPage = useCallback(async (pageNumber: number) => {
    if (!pdfDoc || !documentInfo) return;

    if (pageNumber < 1 || pageNumber > documentInfo.totalPages) {
      console.warn(`Page number ${pageNumber} is out of range`);
      return;
    }

    setIsLoading(true);

    try {
      const { fullText, wordItems } = await extractTextFromPage(pdfDoc, pageNumber);
      setCurrentPageText(fullText);
      setCurrentPageWordItems(wordItems);

      setDocumentInfo({
        ...documentInfo,
        currentPage: pageNumber,
        pdfDoc,
        imageDescriptions: documentInfo.imageDescriptions || [],
      });
    } catch (err) {
      console.error(`Error setting page ${pageNumber}:`, err);
      setError(`Failed to load page ${pageNumber}.`);
    } finally {
      setIsLoading(false);
    }
  }, [pdfDoc, documentInfo]);

  const addHighlight = useCallback((highlight: HighlightArea) => {
    setHighlights(prev => [...prev, highlight]);
  }, []);

  const clearHighlights = useCallback(() => {
    setHighlights([]);
  }, []);

  useEffect(() => {
    return () => {
      if (pdfDoc) {
        pdfDoc.destroy();
      }
    };
  }, [pdfDoc]);

  return {
    pdfDoc,
    documentInfo,
    currentPageText,
    currentPageWordItems,
    highlights,
    isLoading,
    error,
    loadDocument,
    setCurrentPage,
    addHighlight,
    clearHighlights,
  };
};