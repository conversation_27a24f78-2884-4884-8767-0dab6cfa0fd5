import type { WordItem } from '../types';

export interface TextBoundary {
  type: 'word' | 'sentence' | 'paragraph';
  startIndex: number;
  endIndex: number;
  text: string;
}

/**
 * Finds the word boundary at a specific character position
 */
export const findWordBoundary = (
  text: string,
  charIndex: number
): TextBoundary => {
  // Find word boundaries
  const words = text.split(/\s+/);
  let currentIndex = 0;
  
  for (const word of words) {
    const wordStart = currentIndex;
    const wordEnd = currentIndex + word.length;
    
    if (charIndex >= wordStart && charIndex <= wordEnd) {
      return {
        type: 'word',
        startIndex: wordStart,
        endIndex: wordEnd,
        text: word,
      };
    }
    
    currentIndex = wordEnd + 1; // +1 for space
  }
  
  // Fallback: return the character itself
  return {
    type: 'word',
    startIndex: charIndex,
    endIndex: charIndex + 1,
    text: text.charAt(charIndex),
  };
};

/**
 * Finds the sentence boundary at a specific character position
 */
export const findSentenceBoundary = (
  text: string,
  charIndex: number
): TextBoundary => {
  // Find sentence boundaries using common sentence endings
  const sentenceEndings = /[.!?]+\s*/g;
  const sentences: { start: number; end: number; text: string }[] = [];
  
  let lastIndex = 0;
  let match;
  
  while ((match = sentenceEndings.exec(text)) !== null) {
    const sentenceEnd = match.index + match[0].length;
    sentences.push({
      start: lastIndex,
      end: sentenceEnd,
      text: text.substring(lastIndex, sentenceEnd).trim(),
    });
    lastIndex = sentenceEnd;
  }
  
  // Add the last sentence if it doesn't end with punctuation
  if (lastIndex < text.length) {
    sentences.push({
      start: lastIndex,
      end: text.length,
      text: text.substring(lastIndex).trim(),
    });
  }
  
  // Find which sentence contains the character index
  for (const sentence of sentences) {
    if (charIndex >= sentence.start && charIndex <= sentence.end) {
      return {
        type: 'sentence',
        startIndex: sentence.start,
        endIndex: sentence.end,
        text: sentence.text,
      };
    }
  }
  
  // Fallback: return the entire text as one sentence
  return {
    type: 'sentence',
    startIndex: 0,
    endIndex: text.length,
    text: text,
  };
};

/**
 * Finds the paragraph boundary at a specific character position
 */
export const findParagraphBoundary = (
  text: string,
  charIndex: number
): TextBoundary => {
  // Find paragraph boundaries using double line breaks or significant spacing
  const paragraphBreaks = /\n\s*\n|\r\n\s*\r\n/g;
  const paragraphs: { start: number; end: number; text: string }[] = [];
  
  let lastIndex = 0;
  let match;
  
  while ((match = paragraphBreaks.exec(text)) !== null) {
    const paragraphEnd = match.index;
    paragraphs.push({
      start: lastIndex,
      end: paragraphEnd,
      text: text.substring(lastIndex, paragraphEnd).trim(),
    });
    lastIndex = match.index + match[0].length;
  }
  
  // Add the last paragraph
  if (lastIndex < text.length) {
    paragraphs.push({
      start: lastIndex,
      end: text.length,
      text: text.substring(lastIndex).trim(),
    });
  }
  
  // Find which paragraph contains the character index
  for (const paragraph of paragraphs) {
    if (charIndex >= paragraph.start && charIndex <= paragraph.end) {
      return {
        type: 'paragraph',
        startIndex: paragraph.start,
        endIndex: paragraph.end,
        text: paragraph.text,
      };
    }
  }
  
  // Fallback: return the entire text as one paragraph
  return {
    type: 'paragraph',
    startIndex: 0,
    endIndex: text.length,
    text: text,
  };
};

/**
 * Finds the word item that contains a specific character position
 */
export const findWordItemAtPosition = (
  wordItems: WordItem[],
  charIndex: number
): WordItem | null => {
  return wordItems.find(
    item => charIndex >= item.startIndex && charIndex <= item.endIndex
  ) || null;
};

/**
 * Finds the starting position for reading based on clicked coordinates
 */
export const findReadingStartPosition = (
  text: string,
  wordItems: WordItem[],
  clickedWordItem: WordItem | null,
  boundaryType: 'word' | 'sentence' | 'paragraph' = 'word'
): number => {
  if (!clickedWordItem) {
    return 0;
  }
  
  const charIndex = clickedWordItem.startIndex;
  
  switch (boundaryType) {
    case 'word':
      return clickedWordItem.startIndex;
    
    case 'sentence': {
      const boundary = findSentenceBoundary(text, charIndex);
      return boundary.startIndex;
    }
    
    case 'paragraph': {
      const boundary = findParagraphBoundary(text, charIndex);
      return boundary.startIndex;
    }
    
    default:
      return clickedWordItem.startIndex;
  }
};

/**
 * Determines the boundary type based on selection length or user preference
 */
export const determineBoundaryType = (
  selectedText: string
): 'word' | 'sentence' | 'paragraph' => {
  if (!selectedText || selectedText.trim().length === 0) {
    return 'word';
  }
  
  const wordCount = selectedText.trim().split(/\s+/).length;
  
  if (wordCount === 1) {
    return 'word';
  } else if (wordCount <= 20) {
    return 'sentence';
  } else {
    return 'paragraph';
  }
};

/**
 * Gets the word index from character index in the full text
 */
export const getWordIndexFromCharIndex = (
  wordItems: WordItem[],
  charIndex: number
): number | null => {
  const wordItem = findWordItemAtPosition(wordItems, charIndex);
  if (!wordItem) return null;
  
  return wordItems.findIndex(item => 
    item.startIndex === wordItem.startIndex && 
    item.endIndex === wordItem.endIndex
  );
};
