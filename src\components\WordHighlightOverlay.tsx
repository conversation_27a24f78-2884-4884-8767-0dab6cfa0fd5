import React, { useEffect, useState, useRef } from 'react';
import { Box } from '@mui/material';
import type { WordItem } from '../types';

interface WordHighlightOverlayProps {
  wordItems: WordItem[];
  currentWordIndex: number | null;
  pageNumber: number;
  scale: number;
  pageHeight: number;
  isReading: boolean;
  onWordClick?: (wordIndex: number, word: string) => void;
}

const WordHighlightOverlay: React.FC<WordHighlightOverlayProps> = ({
  wordItems,
  currentWordIndex,
  pageNumber,
  scale,
  pageHeight,
  isReading,
  onWordClick,
}) => {
  const [highlightedWords, setHighlightedWords] = useState<Set<number>>(new Set());
  const highlightTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Filter words for current page
  const pageWords = wordItems.filter(word => word.pageIndex === pageNumber - 1);

  // Update highlighted word with animation
  useEffect(() => {
    if (currentWordIndex !== null && isReading) {
      setHighlightedWords(new Set([currentWordIndex]));
      
      // Clear previous timeout
      if (highlightTimeoutRef.current) {
        clearTimeout(highlightTimeoutRef.current);
      }
      
      // Add pulse effect
      highlightTimeoutRef.current = setTimeout(() => {
        setHighlightedWords(new Set());
      }, 300);
    } else if (!isReading) {
      setHighlightedWords(new Set());
    }

    return () => {
      if (highlightTimeoutRef.current) {
        clearTimeout(highlightTimeoutRef.current);
      }
    };
  }, [currentWordIndex, isReading]);

  const handleWordClick = (wordIndex: number, word: string) => {
    if (!isReading && onWordClick) {
      onWordClick(wordIndex, word);
    }
  };

  return (
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 10,
      }}
    >
      {pageWords.map((wordItem, index) => {
        const isCurrentWord = highlightedWords.has(index);
        const isClickable = !isReading;
        
        return (
          <Box
            key={`word-${wordItem.pageIndex}-${wordItem.startIndex}`}
            sx={{
              position: 'absolute',
              left: `${wordItem.coordinates.x * scale}px`,
              top: `${(pageHeight - wordItem.coordinates.y - wordItem.coordinates.height) * scale}px`,
              width: `${wordItem.coordinates.width * scale}px`,
              height: `${wordItem.coordinates.height * scale}px`,
              backgroundColor: isCurrentWord 
                ? 'rgba(255, 235, 59, 0.6)' 
                : isClickable 
                  ? 'transparent' 
                  : 'transparent',
              border: isCurrentWord ? '2px solid #FF5722' : 'none',
              borderRadius: '3px',
              pointerEvents: isClickable ? 'auto' : 'none',
              cursor: isClickable ? 'pointer' : 'default',
              transition: 'all 0.2s ease-in-out',
              transform: isCurrentWord ? 'scale(1.05)' : 'scale(1)',
              boxShadow: isCurrentWord 
                ? '0 0 8px rgba(255, 87, 34, 0.4)' 
                : 'none',
              animation: isCurrentWord ? 'wordPulse 0.5s ease-in-out' : 'none',
              '&:hover': isClickable ? {
                backgroundColor: 'rgba(33, 150, 243, 0.2)',
                transform: 'scale(1.02)',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              } : {},
            }}
            onClick={() => handleWordClick(index, wordItem.word)}
            title={isClickable ? `Click to start reading from "${wordItem.word}"` : undefined}
          />
        );
      })}
      
      <style>{`
        @keyframes wordPulse {
          0% {
            opacity: 0.7;
            transform: scale(1);
          }
          50% {
            opacity: 1;
            transform: scale(1.08);
          }
          100% {
            opacity: 0.9;
            transform: scale(1.05);
          }
        }
      `}</style>
    </Box>
  );
};

export default WordHighlightOverlay;
