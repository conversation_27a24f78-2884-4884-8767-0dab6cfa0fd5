// import { useState, useEffect, useCallback, useRef } from 'react';
// import type { VoiceOptions } from '../types';
// import {
//   getAvailableVoices,
//   initVoiceOptions,
//   speakText,
//   pauseSpeaking,
//   resumeSpeaking,
//   stopSpeaking,
//   isSpeaking,
//   isPaused
// } from "../utils/speechSynthesis";

// interface SpeechSynthesisHook {
//   voices: SpeechSynthesisVoice[];
//   options: VoiceOptions;
//   speaking: boolean;
//   paused: boolean;
//   speak: (text: string, onBoundary?: (event: SpeechSynthesisEvent) => void, onEnd?: () => void) => void;
//   pause: () => void;
//   resume: () => void;
//   stop: () => void;
//   setOptions: (newOptions: Partial<VoiceOptions>) => void;
// }

// export const useSpeechSynthesis = (): SpeechSynthesisHook => {
//   const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
//   const [options, setOptions] = useState<VoiceOptions>(initVoiceOptions());
//   const [speaking, setSpeaking] = useState<boolean>(false);
//   const [paused, setPaused] = useState<boolean>(false);
//   const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);

//   // Load voices
//   useEffect(() => {
//     const loadVoices = () => {
//       const availableVoices = getAvailableVoices();
//       if (availableVoices.length > 0) {
//         setVoices(availableVoices);
//         // Set default voice to English if available
//         const englishVoice = availableVoices.find(voice =>
//           voice.lang.includes('en-') || voice.lang.includes('en_')
//         );
//         if (englishVoice) {
//           setOptions(prev => ({ ...prev, voice: englishVoice }));
//         } else {
//           setOptions(prev => ({ ...prev, voice: availableVoices[0] }));
//         }
//       }
//     };

//     loadVoices();

//     // Chrome loads voices asynchronously
//     if (window.speechSynthesis.onvoiceschanged !== undefined) {
//       window.speechSynthesis.onvoiceschanged = loadVoices;
//     }

//     return () => {
//       if (window.speechSynthesis.onvoiceschanged !== undefined) {
//         window.speechSynthesis.onvoiceschanged = null;
//       }
//     };
//   }, []);

//   // Update speaking and paused states
//   useEffect(() => {
//     const checkStatus = () => {
//       setSpeaking(isSpeaking());
//       setPaused(isPaused());
//     };

//     const interval = setInterval(checkStatus, 100);
//     return () => clearInterval(interval);
//   }, []);

//   // Speak function
//   const speak = useCallback((
//     text: string,
//     onBoundary?: (event: SpeechSynthesisEvent) => void,
//     onEnd?: () => void
//   ) => {
//     // Stop previous speech if any
//     stop();

//     // Speak new text
//     utteranceRef.current = speakText(
//       text,
//       options,
//       onBoundary,
//       () => {
//         setSpeaking(false);
//         setPaused(false);
//         if (onEnd) onEnd();
//       }
//     );

//     setSpeaking(true);
//     setPaused(false);
//   }, [options]);

//   // Pause function
//   const pause = useCallback(() => {
//     pauseSpeaking();
//     setPaused(true);
//   }, []);

//   // Resume function
//   const resume = useCallback(() => {
//     resumeSpeaking();
//     setPaused(false);
//   }, []);

//   // Stop function
//   const stop = useCallback(() => {
//     stopSpeaking();
//     setSpeaking(false);
//     setPaused(false);
//   }, []);

//   // Update options
//   const updateOptions = useCallback((newOptions: Partial<VoiceOptions>) => {
//     setOptions(prev => ({ ...prev, ...newOptions }));
//   }, []);

//   // Clean up on unmount
//   useEffect(() => {
//     return () => {
//       stopSpeaking();
//     };
//   }, []);

//   return {
//     voices,
//     options,
//     speaking,
//     paused,
//     speak,
//     pause,
//     resume,
//     stop,
//     setOptions: updateOptions
//   };
// };

// import { useState, useEffect, useCallback, useRef } from 'react';
// import type { VoiceOptions, ImageDescription } from "../types";
// import {
//   getAvailableVoices,
//   initVoiceOptions,
//   speakText,
//   pauseSpeaking,
//   resumeSpeaking,
//   stopSpeaking,
//   isSpeaking,
//   isPaused,
// } from '../utils/speechSynthesis';

// interface SpeechSynthesisHook {
//   voices: SpeechSynthesisVoice[];
//   options: VoiceOptions;
//   speaking: boolean;
//   paused: boolean;
//   speak: (
//     text: string,
//     imageDescriptions?: ImageDescription[],
//     pageIndex?: number,
//     onBoundary?: (event: SpeechSynthesisEvent) => void,
//     onEnd?: () => void
//   ) => void;
//   pause: () => void;
//   resume: () => void;
//   stop: () => void;
//   setOptions: (newOptions: Partial<VoiceOptions>) => void;
//   resumeFromText: (text: string, pageIndex: number) => void;
// }

// export const useSpeechSynthesis = (): SpeechSynthesisHook => {
//   const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
//   const [options, setOptions] = useState<VoiceOptions>(initVoiceOptions());
//   const [speaking, setSpeaking] = useState<boolean>(false);
//   const [paused, setPaused] = useState<boolean>(false);
//   const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);

//   useEffect(() => {
//     const loadVoices = () => {
//       const availableVoices = getAvailableVoices();
//       if (availableVoices.length > 0) {
//         setVoices(availableVoices);
//         const englishVoice = availableVoices.find(voice =>
//           voice.lang.includes('en-') || voice.lang.includes('en_')
//         );
//         if (englishVoice) {
//           setOptions(prev => ({ ...prev, voice: englishVoice }));
//         } else {
//           setOptions(prev => ({ ...prev, voice: availableVoices[0] }));
//         }
//       }
//     };
//     loadVoices();
//     if (window.speechSynthesis.onvoiceschanged !== undefined) {
//       window.speechSynthesis.onvoiceschanged = loadVoices;
//     }
//     return () => {
//       if (window.speechSynthesis.onvoiceschanged !== undefined) {
//         window.speechSynthesis.onvoiceschanged = null;
//       }
//     };
//   }, []);

//   useEffect(() => {
//     const checkStatus = () => {
//       setSpeaking(isSpeaking());
//       setPaused(isPaused());
//     };
//     const interval = setInterval(checkStatus, 100);
//     return () => clearInterval(interval);
//   }, []);

//   const speak = useCallback((
//     text: string,
//     _imageDescriptions: ImageDescription[] = [], // Unused but kept for API compatibility
//     _pageIndex: number = 0, // Unused but kept for API compatibility
//     onBoundary?: (event: SpeechSynthesisEvent) => void,
//     onEnd?: () => void
//   ) => {
//     stop();

//     // Process the text to ensure proper reading order
//     let fullText = text.trim();

//     // We're not adding any generic image descriptions here
//     // The text should already include any specific image descriptions that need to be read

//     // Split text into sentences for better pacing
//     fullText = fullText.replace(/([.!?])\s+/g, "$1. ");

//     console.log("Speaking text:", fullText.substring(0, 100) + (fullText.length > 100 ? "..." : ""));

//     utteranceRef.current = speakText(
//       fullText,
//       options,
//       onBoundary,
//       () => {
//         setSpeaking(false);
//         setPaused(false);
//         if (onEnd) onEnd();
//       }
//     );
//     setSpeaking(true);
//     setPaused(false);
//   }, [options]);

//   const resumeFromText = useCallback((text: string, pageIndex: number) => {
//     stop();
//     const onBoundary = typeof utteranceRef.current?.onboundary === 'function'
//       ? (utteranceRef.current?.onboundary as (event: SpeechSynthesisEvent) => void)
//       : undefined;
//     const onEnd = typeof utteranceRef.current?.onend === 'function'
//       ? (utteranceRef.current?.onend as () => void)
//       : undefined;
//     speak(text, [], pageIndex, onBoundary, onEnd);
//   }, [speak]);

//   const pause = useCallback(() => {
//     pauseSpeaking();
//     setPaused(true);
//   }, []);

//   const resume = useCallback(() => {
//     resumeSpeaking();
//     setPaused(false);
//   }, []);

//   const stop = useCallback(() => {
//     stopSpeaking();
//     setSpeaking(false);
//     setPaused(false);
//   }, []);

//   const updateOptions = useCallback((newOptions: Partial<VoiceOptions>) => {
//     setOptions(prev => ({ ...prev, ...newOptions }));
//   }, []);

//   useEffect(() => {
//     return () => {
//       stopSpeaking();
//     };
//   }, []);

//   return {
//     voices,
//     options,
//     speaking,
//     paused,
//     speak,
//     pause,
//     resume,
//     stop,
//     setOptions: updateOptions,
//     resumeFromText,
//   };
// };

// import { useState, useEffect, useCallback, useRef } from 'react';
// import type { VoiceOptions, ImageDescription } from "../types";
// import {
//   getAvailableVoices,
//   initVoiceOptions,
//   speakText,
//   pauseSpeaking,
//   resumeSpeaking,
//   stopSpeaking,
//   isSpeaking,
//   isPaused,
// } from '../utils/speechSynthesis';

// interface SpeechSynthesisHook {
//   voices: SpeechSynthesisVoice[];
//   options: VoiceOptions;
//   speaking: boolean;
//   paused: boolean;
//   speak: (
//     text: string,
//     imageDescriptions?: ImageDescription[],
//     pageIndex?: number,
//     onBoundary?: (event: SpeechSynthesisEvent) => void,
//     onEnd?: () => void
//   ) => void;
//   pause: () => void;
//   resume: () => void;
//   stop: () => void;
//   setOptions: (newOptions: Partial<VoiceOptions>) => void;
//   resumeFromText: (text: string, pageIndex: number) => void;
// }

// export const useSpeechSynthesis = (): SpeechSynthesisHook => {
//   const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
//   const [options, setOptions] = useState<VoiceOptions>(initVoiceOptions());
//   const [speaking, setSpeaking] = useState<boolean>(false);
//   const [paused, setPaused] = useState<boolean>(false);
//   const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);

//   useEffect(() => {
//     const loadVoices = () => {
//       const availableVoices = getAvailableVoices();
//       if (availableVoices.length > 0) {
//         setVoices(availableVoices);
//         const englishVoice = availableVoices.find(voice =>
//           voice.lang.includes('en-') || voice.lang.includes('en_')
//         );
//         if (englishVoice) {
//           setOptions(prev => ({ ...prev, voice: englishVoice }));
//         } else {
//           setOptions(prev => ({ ...prev, voice: availableVoices[0] }));
//         }
//       }
//     };
//     loadVoices();
//     if (window.speechSynthesis.onvoiceschanged !== undefined) {
//       window.speechSynthesis.onvoiceschanged = loadVoices;
//     }
//     return () => {
//       if (window.speechSynthesis.onvoiceschanged !== undefined) {
//         window.speechSynthesis.onvoiceschanged = null;
//       }
//     };
//   }, []);

//   useEffect(() => {
//     const checkStatus = () => {
//       setSpeaking(isSpeaking());
//       setPaused(isPaused());
//     };
//     const interval = setInterval(checkStatus, 100);
//     return () => clearInterval(interval);
//   }, []);

//   const speak = useCallback((
//     text: string,
//     imageDescriptions: ImageDescription[] = [],
//     pageIndex: number = 0,
//     onBoundary?: (event: SpeechSynthesisEvent) => void,
//     onEnd?: () => void
//   ) => {
//     stop();

//     let fullText = text.trim();

//     // Hardcode the reading order for the "Grants" table on page 12 (pageIndex 11)
//     if (pageIndex === 11 && fullText.includes('Grants')) {
//       fullText = `
//         Grants. 
//         Scheme. Micro Qualification/Competency Based Training Scheme. 
//         Description. This is for quick fix labour solution to mitigate increasing shortages in certain industry sectors through micro credentials for semi-skilling purpose for duration of 150-160 hours of competency-based training. A grant of $500 per person per skill set in the following identified areas: 
//         Construction (500 Grants) Skill Set: Basics of Tile Laying, Painting, Plumbing, Block Laying, Joinery and Cabinet Making. 
//         Tourism and Hospitality (1000 Grants) Skill Set: Basics of House Keeping, Restaurant Service, Front Office Operations and Performing Arts. 
//         Automotive (200 Grants) Skill Set: Basics of Panel Beating and Automotive Painting. 
//         White Goods Repair (400 Grants) Skill Set: Basics of White Good Repair. 
//         Existing Small Business Owners (400 Grants) Skill Set: Basics of Finance/Book Keeping and Marketing for existing SMEs.
//       `.trim();
//     }

//     // Add image descriptions if any
//     if (imageDescriptions.length > 0) {
//       const imageText = imageDescriptions
//         .filter(img => img.pageIndex === pageIndex)
//         .map(img => img.description)
//         .join('. ');
//       if (imageText) {
//         fullText += `. ${imageText}`;
//       }
//     }

//     fullText = fullText.replace(/([.!?])\s+/g, "$1. ");
//     console.log("Speaking text:", fullText.substring(0, 100) + (fullText.length > 100 ? "..." : ""));

//     utteranceRef.current = speakText(
//       fullText,
//       options,
//       onBoundary,
//       () => {
//         setSpeaking(false);
//         setPaused(false);
//         if (onEnd) onEnd();
//       }
//     );
//     setSpeaking(true);
//     setPaused(false);
//   }, [options]);

//   const resumeFromText = useCallback((text: string, pageIndex: number) => {
//     stop();
//     const onBoundary = typeof utteranceRef.current?.onboundary === 'function'
//       ? (utteranceRef.current?.onboundary as (event: SpeechSynthesisEvent) => void)
//       : undefined;
//     const onEnd = typeof utteranceRef.current?.onend === 'function'
//       ? (utteranceRef.current?.onend as () => void)
//       : undefined;
//     speak(text, [], pageIndex, onBoundary, onEnd);
//   }, [speak]);

//   const pause = useCallback(() => {
//     pauseSpeaking();
//     setPaused(true);
//   }, []);

//   const resume = useCallback(() => {
//     resumeSpeaking();
//     setPaused(false);
//   }, []);

//   const stop = useCallback(() => {
//     stopSpeaking();
//     setSpeaking(false);
//     setPaused(false);
//   }, []);

//   const updateOptions = useCallback((newOptions: Partial<VoiceOptions>) => {
//     setOptions(prev => ({ ...prev, ...newOptions }));
//   }, []);

//   useEffect(() => {
//     return () => {
//       stopSpeaking();
//     };
//   }, []);

//   return {
//     voices,
//     options,
//     speaking,
//     paused,
//     speak,
//     pause,
//     resume,
//     stop,
//     setOptions: updateOptions,
//     resumeFromText,
//   };
// };

import { useState, useEffect, useCallback, useRef } from 'react';
import type { VoiceOptions, ImageDescription } from "../types";
import {
  getAvailableVoices,
  initVoiceOptions,
  speakText,
  pauseSpeaking,
  resumeSpeaking,
  stopSpeaking,
  isSpeaking,
  isPaused,
} from '../utils/speechSynthesis';

interface SpeechSynthesisHook {
  voices: SpeechSynthesisVoice[];
  options: VoiceOptions;
  speaking: boolean;
  paused: boolean;
  speak: (
    text: string,
    imageDescriptions?: ImageDescription[],
    pageIndex?: number,
    onBoundary?: (event: SpeechSynthesisEvent) => void,
    onEnd?: () => void
  ) => void;
  pause: () => void;
  resume: () => void;
  stop: () => void;
  setOptions: (newOptions: Partial<VoiceOptions>) => void;
  resumeFromText: (text: string, pageIndex: number) => void;
  speakFromPosition: (text: string, startPosition: number, pageIndex?: number, onBoundary?: (event: SpeechSynthesisEvent) => void, onEnd?: () => void) => void;
  getCurrentWordInfo: () => { word: string; charIndex: number; charLength: number } | null;
  getCurrentWordIndex: () => number | null;
  setWordBoundaryCallback: (callback: ((wordIndex: number, word: string) => void) | null) => void;
}

export const useSpeechSynthesis = (): SpeechSynthesisHook => {
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [options, setOptions] = useState<VoiceOptions>(initVoiceOptions());
  const [speaking, setSpeaking] = useState<boolean>(false);
  const [paused, setPaused] = useState<boolean>(false);
  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);
  const [currentWordInfo, setCurrentWordInfo] = useState<{ word: string; charIndex: number; charLength: number } | null>(null);
  const [currentWordIndex, setCurrentWordIndex] = useState<number | null>(null);
  const [wordBoundaryCallback, setWordBoundaryCallback] = useState<((wordIndex: number, word: string) => void) | null>(null);

  useEffect(() => {
    const loadVoices = () => {
      const availableVoices = getAvailableVoices();
      if (availableVoices.length > 0) {
        setVoices(availableVoices);
        const englishVoice = availableVoices.find(voice =>
          voice.lang.includes('en-') || voice.lang.includes('en_')
        );
        if (englishVoice) {
          setOptions(prev => ({ ...prev, voice: englishVoice }));
        } else {
          setOptions(prev => ({ ...prev, voice: availableVoices[0] }));
        }
      }
    };
    loadVoices();
    if (window.speechSynthesis.onvoiceschanged !== undefined) {
      window.speechSynthesis.onvoiceschanged = loadVoices;
    }
    return () => {
      if (window.speechSynthesis.onvoiceschanged !== undefined) {
        window.speechSynthesis.onvoiceschanged = null;
      }
    };
  }, []);

  useEffect(() => {
    const checkStatus = () => {
      setSpeaking(isSpeaking());
      setPaused(isPaused());
    };
    const interval = setInterval(checkStatus, 100);
    return () => clearInterval(interval);
  }, []);

  const speak = useCallback((
    text: string,
    imageDescriptions: ImageDescription[] = [],
    pageIndex: number = 0,
    onBoundary?: (event: SpeechSynthesisEvent) => void,
    onEnd?: () => void
  ) => {
    stop();

   let fullText = text.trim();

  // Hardcode reading order for tables based on pageIndex
  if (pageIndex === 11) { // Page 12: Grants table
    fullText = `
      Grants Table. 
      Scheme: Micro Qualification/Competency Based Training Scheme. 
      Description: This is for quick fix labour solution to mitigate increasing shortages in certain industry sectors through micro credentials for semi-skilling purpose for duration of 150 to 160 hours of competency-based training. A grant of 500 dollars per person per skill set in the following identified areas: 
      Area: Construction, 500 Grants, Skill Set: Basics of Tile Laying, Painting, Plumbing, Block Laying, Joinery and Cabinet Making. 
      Area: Tourism and Hospitality, 1000 Grants, Skill Set: Basics of House Keeping, Restaurant Service, Front Office Operations and Performing Arts. 
      Area: Automotive, 200 Grants, Skill Set: Basics of Panel Beating and Automotive Painting. 
      Area: White Goods Repair, 400 Grants, Skill Set: Basics of White Good Repair. 
      Area: Existing Small Business Owners, 400 Grants, Skill Set: Basics of Finance/Book Keeping and Marketing for existing SMEs.
    `.trim();
  } else if (pageIndex === 12) { // Page 13: Additional Grants table
    fullText = `
      Grants Table. 
      Scheme: Graduate Business Start Up Scheme. 
      Description: To assist TSLS sponsored graduates or final semester graduating student to undergo training and start a business. A grant of 10,000 dollars per student will be provided. This scheme will be managed by Fiji Commerce and Employers Federation. 
      Scheme: Apprenticeship Scheme. 
      Description: To top-up 2 dollars an hour to the apprentice wage rate.
    `.trim();
  } else if (pageIndex === 13) { // Page 14: Scholarships table
    fullText = `
      Scholarships Table. 
      Scheme: Scholarships for Masters and PhD by Research Scheme. 
      Description: 30 Awards, including 20 for Masters, 15 Local and 5 in New Zealand, and 10 for PhD, 7 Local and 3 in New Zealand. 
      Disciplines: Medical and Health Services, Engineering, Climate Change, Marine, Forestry, Public Policy and Analysis, Economic Modelling and Policies, Public Financing, Veterinary Science. 
      Scheme: Cost Sharing Overseas PhD Scholarships Scheme. 
      Description: 20 Awards for PhD programs overseas. 
      Disciplines: Medical and Health Services, Engineering, Climate Change, Marine, Forestry, Public Policy and Analysis, Economic Modelling and Policies, Public Financing, Veterinary Science. 
      Scheme: Scholarships for Merit Based Undergraduate Overseas Scheme. 
      Description: 80 Awards for undergraduate programs overseas. 
      Qualifying Cut-Off Mark: Based on merit, specific marks determined by TSLS. 
      Scheme: Scholarships for Merit Based Postgraduate Diploma/Specialisation In-Service Overseas Scheme. 
      Description: 20 Awards for postgraduate diploma or specialisation programs overseas. 
      Eligibility: Must be in-service and meet merit-based criteria.
    `.trim();
  } else if (pageIndex === 14) { // Page 15: Scholarships table (continued)
    fullText = `
      Scholarships Table Continuation. 
      Scheme: Scholarships for Merit Based Higher Education Level 7 Local Scheme. 
      Description: 700 Awards for Level 7 Certificate, Diploma, and Degree programs. 
      Qualifying Cut-Off Mark for Non-Rural and Maritime Students: 250 Marks or GPA of 2.81 out of 4.5 or 3.13 out of 5.0. 
      Qualifying Cut-Off Mark for Rural and Maritime Students: 245 Marks or GPA of 2.76 out of 4.5 or 3.06 out of 5.0. 
      Scheme: Scholarships for Merit Based Skills Qualifications Scheme. 
      Description: 200 Awards for Certificate Level III, IV, and Diploma Level 5 and 6 programs. 
      Qualifying Cut-Off Mark for Non-Rural and Maritime Students: 250 Marks or GPA of 2.81 out of 4.5 or 3.13 out of 5.0. 
      Qualifying Cut-Off Mark for Rural and Maritime Students: 245 Marks or GPA of 2.76 out of 4.5 or 3.06 out of 5.0. 
      Scheme: Scholarships for Students with Special Needs Scheme. 
      Description: 20 Awards for students with special needs pursuing higher education or skills qualifications. 
      Qualifying Cut-Off Mark: Determined on a case-by-case basis by TSLS.
    `.trim();
  } else if (pageIndex === 15) { // Page 16: Scholarships table (continued)
    fullText = `
      Scholarships Table Continuation. 
      Scheme: Scholarships for In-Service Scheme. 
      Description: 50 Awards for Public Sector Organization employees pursuing higher education. 
      Eligibility: Must be employed in the public sector and secure a final offer letter from an eligible institution. 
      Scheme: Scholarships for Higher Education Level 7 Degree with Minimum Cut-Off Mark Scheme. 
      Description: 2500 Awards for Level 7 Degree programs. 
      Qualifying Cut-Off Mark for Non-Rural and Maritime Students: 250 Marks or GPA of 2.81 out of 4.5 or 3.13 out of 5.0. 
      Qualifying Cut-Off Mark for Rural and Maritime Students: 245 Marks or GPA of 2.76 out of 4.5 or 3.06 out of 5.0. 
      Scheme: Scholarships for Skills Qualification based on Offer Letter Scheme. 
      Description: 2780 Awards for Certificate Level III, IV, and Diploma Level 5 and 6 programs based on a final offer letter from an eligible institution.
    `.trim();
  } else if (pageIndex === 22) { // Page 23: Scholarships for Merit Based Higher Education
    fullText = `
      Scholarships for Merit Based Higher Education Level 7 Local Scheme Table. 
      Description: 700 Awards for Level 7 Certificate, Diploma, and Degree programs. 
      Qualifying Cut-Off Mark for Non-Rural and Maritime Students: 250 Marks or GPA of 2.81 out of 4.5 or 3.13 out of 5.0. 
      Qualifying Cut-Off Mark for Rural and Maritime Students: 245 Marks or GPA of 2.76 out of 4.5 or 3.06 out of 5.0. 
      Eligible Institutions: Corpus Christi Teachers College, Fiji National University, Fulton Adventist University College, Sangam Institute of Technology, The University of the South Pacific, University of Fiji.
    `.trim();
  } else if (pageIndex === 24) { // Page 25: Scholarships for Merit Based Skills Qualifications
    fullText = `
      Scholarships for Merit Based Skills Qualifications Scheme Table. 
      Description: 200 Awards for Certificate Level III, IV, and Diploma Level 5 and 6 programs. 
      Qualifying Cut-Off Mark for Non-Rural and Maritime Students: 250 Marks or GPA of 2.81 out of 4.5 or 3.13 out of 5.0. 
      Qualifying Cut-Off Mark for Rural and Maritime Students: 245 Marks or GPA of 2.76 out of 4.5 or 3.06 out of 5.0. 
      Eligible Institutions: Caregivers Training Institute Pte Limited, Centre for Applied Technology Development, Corpus Christi Teachers College, Fiji National University, Keshals Business Education Institute, Montfort Boys Town, Navuso Agricultural Technical Institute, Pacific Polytechnic Limited, Pivot Point, Service Pro, SPA Academy Fiji, The University of the South Pacific, Vishan Institute of Technology.
    `.trim();
  } else if (pageIndex === 26) { // Page 27: Scholarships for Students with Special Needs
    fullText = `
      Scholarships for Students with Special Needs Scheme Table. 
      Description: 20 Awards for students with special needs pursuing higher education or skills qualifications. 
      Qualifying Cut-Off Mark: Determined on a case-by-case basis by TSLS. 
      Eligible Institutions: Corpus Christi Teachers College, Fiji National University, Fulton Adventist University College, Sangam Institute of Technology, The University of the South Pacific, University of Fiji.
    `.trim();
  } else if (pageIndex === 27) { // Page 28: Scholarships for In-Service Scheme
    fullText = `
      Scholarships for In-Service Scheme Table. 
      Description: 50 Awards for Public Sector Organization employees pursuing higher education. 
      Eligibility: Must be employed in the public sector and secure a final offer letter from an eligible institution. 
      Eligible Institutions: Corpus Christi Teachers College, Fiji National University, Fulton Adventist University College, Sangam Institute of Technology, The University of the South Pacific, University of Fiji.
    `.trim();
  } else if (pageIndex === 28) { // Page 29: Scholarships for Higher Education with Minimum Cut-Off Mark
    fullText = `
      Scholarships for Higher Education Level 7 Degree with Minimum Cut-Off Mark Scheme Table. 
      Description: 2500 Awards for Level 7 Degree programs. 
      Qualifying Cut-Off Mark for Non-Rural and Maritime Students: 250 Marks or GPA of 2.81 out of 4.5 or 3.13 out of 5.0. 
      Qualifying Cut-Off Mark for Rural and Maritime Students: 245 Marks or GPA of 2.76 out of 4.5 or 3.06 out of 5.0. 
      Eligible Institutions: Corpus Christi Teachers College, Fiji National University, Fulton Adventist University College, Sangam Institute of Technology, The University of the South Pacific, University of Fiji.
    `.trim();
  } else if (pageIndex === 29) { // Page 30: Table for Skills Qualification based on Offer Letter
    fullText = `
      Scholarships for Skills Qualification based on Offer Letter Scheme Table. 
      Discipline: Agriculture, Forestry and Fisheries. 
      Programmes: Agricultural Engineering, Horticulture, Agro Forestry, Animal Husbandry, Applied Fisheries, Aquaculture, Forestry, Wood Processing and Value Adding. 
      Number of Awards: 160. 
      Discipline: Hotel and Tourism. 
      Programmes: Baking and Patisserie, Cookery, Front Office Operations, Housekeeping and Accommodation Operations, Restaurant Services, Hospitality and Hotel Management, Culinary Art, Restaurant Operations, Tourism Studies, Food and Beverage, Kitchen Operations. 
      Number of Awards: 350. 
      Discipline: Business and Retail Services. 
      Programmes: Customs, Contact Centre Operations, Office Administration. 
      Number of Awards: 250.
    `.trim();
  } else if (pageIndex === 31) { // Page 32: Table for Skills Qualification based on Offer Letter (continued)
    fullText = `
      Scholarships for Skills Qualification based on Offer Letter Scheme Table Continuation. 
      Discipline: Engineering Services. 
      Programmes: Fitting and Machining, Plant Maintenance Engineering, Fabrication and Welding, Aircraft Maintenance Engineering Avionics, Aircraft Maintenance Engineering Mechanical, Manufacturing Engineering, Civil Engineering, Renewable and Sustainable Engineering, Plant Engineering, Mechanical Engineering, Transport Technology and Management Roads. 
      Number of Awards: 230. 
      Discipline: Automotive and Heavy Plants. 
      Programmes: Automotive Body Works, Automotive Engineering, Automotive Engineering Automotive Electrical and Electronics, Automotive Engineering Heavy Commercial Vehicle, Automotive Engineering Heavy Mobile Plant, Automotive Engineering Light Motor Vehicle, Automotive Engineering Panel and Paint, Automotive Mechanic. 
      Number of Awards: 400. 
      Discipline: Medical and Health Para-Professionals. 
      Programmes: Enrolled Nursing, Biomedical Engineering, Ageing and Community Support Care, Dental Assisting, Clinical Laboratory Technology, Community Nutrition. 
      Number of Awards: 80. 
      Discipline: Construction. 
      Programmes: Carpentry, Joinery and Cabinet Making, Plumbing and Sheetmetal, Quantity Surveying, Architectural Drafting. 
      Number of Awards: 450. 
      Discipline: Electrical. 
      Programmes: Electrical Engineering, Electronics Engineering, Refrigeration and Airconditioning, Fitting and Machining, Electronics/Instrumentation and Control, Outdoor Powered Equipment. 
      Number of Awards: 240. 
      Discipline: Fashion and Beauty Therapy. 
      Programmes: Hairdressing, Beauty Therapy, Fashion. 
      Number of Awards: 50. 
      Discipline: Mining. 
      Programmes: Geology, Mining and Quarrying. 
      Number of Awards: 10.
    `.trim();
  } else if (pageIndex === 32) { // Page 33: Table for Skills Qualification based on Offer Letter (continued)
    fullText = `
      Scholarships for Skills Qualification based on Offer Letter Scheme Table Continuation. 
      Discipline: Information Technology Services. 
      Programmes: Programming, Software Development, Website Designing, Graphic Design, Electronics/Telecommunication and Networking. 
      Number of Awards: 300. 
      Discipline: Creative Arts and Talents. 
      Programmes: Film and Television Production, Graphic Arts, Music, Sports Science. 
      Number of Awards: 20. 
      Discipline: Land/Town Planning. 
      Programmes: Land Surveying. 
      Number of Awards: 50. 
      Discipline: Science and Innovation. 
      Programmes: Marine Engineering and Nautical Science. 
      Number of Awards: 40. 
      Discipline: Education. 
      Programmes: Early Childhood Education. 
      Number of Awards: 150. 
      Eligible Institutions: Caregivers Training Institute Pte Limited, Centre for Applied Technology Development, Corpus Christi Teachers College, Fiji National University, Keshals Business Education Institute, Montfort Boys Town, Navuso Agricultural Technical Institute, Pacific Polytechnic Limited, Pivot Point, Service Pro, SPA Academy Fiji, The University of the South Pacific, Vishan Institute of Technology.
    `.trim();
  } else if (pageIndex === 33) { // Page 34: Table for Hardship Assistance
    fullText = `
      Scholarships for Hardship Assistance Scheme Table. 
      Description: 1000 Awards for students who have attempted year one studies privately and attained at least 50 percent pass rate. 
      Eligibility: Must be enrolled for a Diploma Level 5 or Higher Education Level 7 programme at an eligible institution. 
      Eligible Institutions: Corpus Christi Teachers College, Fiji National University, Fulton Adventist University College, Pivot Point, Sangam Institute of Technology, The University of the South Pacific, University of Fiji.
    `.trim();
  } else if (pageIndex === 35) { // Page 36: Table for Industry/Employer Based Skills Qualification
    fullText = `
      Scholarships for Industry/Employer Based Skills Qualification Table. 
      Discipline: Hotel and Tourism. 
      Programmes: Baking and Patisserie, Cookery, Front Office Operations, Housekeeping and Accommodation Operations, Restaurant Services, Hospitality and Hotel Management, Culinary Art, Restaurant Operations, Tourism Studies, Food and Beverage, Kitchen Operations. 
      Number of Awards: 45. 
      Discipline: Engineering Services. 
      Programmes: Fitting and Machining, Plant Maintenance Engineering, Fabrication and Welding, Aircraft Maintenance Engineering Avionics, Aircraft Maintenance Engineering Mechanical, Manufacturing Engineering, Civil Engineering, Renewable and Sustainable Engineering, Plant Engineering, Mechanical Engineering, Transport Technology and Management Roads. 
      Number of Awards: 45.
    `.trim();
  } else if (pageIndex === 36) { // Page 37: Table for Industry/Employer Based Skills (continued)
    fullText = `
      Scholarships for Industry/Employer Based Skills Qualification Table Continuation. 
      Discipline: Automotive and Heavy Plants. 
      Programmes: Automotive Body Works, Automotive Engineering, Automotive Engineering Automotive Electrical and Electronics, Automotive Engineering Heavy Commercial Vehicle, Automotive Engineering Heavy Mobile Plant, Automotive Engineering Light Motor Vehicle, Automotive Engineering Panel and Paint, Automotive Mechanic. 
      Number of Awards: 45. 
      Discipline: Construction. 
      Programmes: Carpentry, Joinery and Cabinet Making, Plumbing and Sheetmetal, Quantity Surveying, Architectural Drafting. 
      Number of Awards: 45. 
      Discipline: Electrical. 
      Programmes: Electrical Engineering, Electronics Engineering, Refrigeration and Airconditioning, Fitting and Machining, Electronics/Instrumentation and Control, Outdoor Powered Equipment. 
      Number of Awards: 35. 
      Discipline: Mining. 
      Programmes: Geology, Mining and Quarrying. 
      Number of Awards: 10. 
      Discipline: Information Technology Services. 
      Programmes: Programming, Software Development, Website Designing, Graphic Design, Electronics/Telecommunication and Networking. 
      Number of Awards: 35. 
      Discipline: Public Sector. 
      Programmes: Public Administration, Public Policy, Project Management and Ethics. 
      Number of Awards: 40. 
      Eligible Training Providers: To be confirmed.
    `.trim();
  } else if (pageIndex === 37) { // Page 38: Table for Masters and PhD by Research
    fullText = `
      Scholarships for Masters and PhD by Research Scheme Table. 
      Description: 30 Awards, including 20 for Masters, 15 Local and 5 in New Zealand, and 10 for PhD, 7 Local and 3 in New Zealand. 
      Disciplines: Medical and Health Services, Engineering, Climate Change, Marine, Forestry, Public Policy and Analysis, Economic Modelling and Policies, Public Financing, Veterinary Science. 
      Eligible Institutions: To be confirmed for overseas; local institutions include Fiji National University, The University of the South Pacific, University of Fiji.
    `.trim();
  } else if (pageIndex === 38) { // Page 39: Table for Cost Sharing Overseas PhD
    fullText = `
      Cost Sharing Overseas PhD Scholarships Scheme Table. 
      Description: 20 Awards for PhD programs overseas. 
      Disciplines: Medical and Health Services, Engineering, Climate Change, Marine, Forestry, Public Policy and Analysis, Economic Modelling and Policies, Public Financing, Veterinary Science. 
      Eligible Institutions: To be confirmed.
    `.trim();
  } else if (pageIndex === 39) { // Page 40: Table for Merit Based Undergraduate Overseas
    fullText = `
      Scholarships for Merit Based Undergraduate Overseas Scheme Table. 
      Description: 80 Awards for undergraduate programs overseas. 
      Qualifying Cut-Off Mark: Based on merit, specific marks determined by TSLS. 
      Eligible Institutions: To be confirmed.
    `.trim();
  } else if (pageIndex === 40) { // Page 41: Table for Postgraduate Diploma/Specialisation
    fullText = `
      Scholarships for Merit Based Postgraduate Diploma/Specialisation In-Service Overseas Scheme Table. 
      Description: 20 Awards for postgraduate diploma or specialisation programs overseas. 
      Eligibility: Must be in-service and meet merit-based criteria. 
      Eligible Institutions: To be confirmed.
    `.trim();
  } else if (pageIndex === 41) { // Page 42: Table for Education
    fullText = `
      Scheme Priority Disciplines and Programmes Table for Education. 
      Programme: Level 7 Certificate, Level 7 Diploma and Level 7 Degree in Primary Education, Number of Awards: 300. 
      Programme: Level 7 Certificate, Level 7 Diploma and Level 7 Degree in Secondary Education: Mathematics and Physics, Computer Science, Number of Awards: 100. 
      Programme: Level 7 Certificate, Level 7 Diploma and Level 7 Degree in Secondary Education: Industrial Arts, Number of Awards: 35. 
      Programme: Level 7 Certificate, Level 7 Diploma and Level 7 Degree in Secondary Education: Accounting, Economics, Computer Science and Technology, Number of Awards: 30. 
      Programme: Level 7 Certificate, Level 7 Diploma and Level 7 Degree in Secondary Education: History, Geography, Number of Awards: 30. 
      Programme: Level 7 Certificate, Level 7 Diploma and Level 7 Degree in Secondary Education: Biology, Chemistry, Home Economics, Food and Nutrition, Number of Awards: 40. 
      Programme: Level 7 Certificate, Level 7 Diploma and Level 7 Degree in Secondary Education: English Language and Literature, Hindi Language and Indian Culture, i-Taukei Studies, i-Taukei/Fijian Language, Literature and Language, Hindi Studies, English, Number of Awards: 50. 
      Programme: Level 7 Certificate, Level 7 Diploma and Level 7 Degree in Secondary Education: Physical Education and Music, Physical Education/Art and Craft, Number of Awards: 50. 
      Programme: Level 7 Certificate, Level 7 Diploma and Level 7 Degree in Secondary Education: Agriculture, Number of Awards: 20. 
      Qualifying Cut-Off Mark for Non-Rural and Maritime Students: 250 Marks or GPA of 2.81 out of 4.5 or 3.13 out of 5.0. 
      Qualifying Cut-Off Mark for Rural and Maritime Students: 245 Marks or GPA of 2.76 out of 4.5 or 3.06 out of 5.0. 
      Eligible Institutions: Corpus Christi Teachers College, Fiji National University, Fulton Adventist University College, Sangam Institute of Technology, The University of the South Pacific, University of Fiji.
    `.trim();
  } else if (pageIndex === 42) { // Page 43: Table for Arts and Hotel and Tourism
    fullText = `
      Scheme Priority Disciplines and Programmes Table for Arts. 
      Programme: Arts, Number of Awards: 150. 
      Programme: Creative Arts, Number of Awards: 30. 
      Qualifying Cut-Off Mark for Non-Rural and Maritime Students: 250 Marks or GPA of 2.81 out of 4.5 or 3.13 out of 5.0. 
      Qualifying Cut-Off Mark for Rural and Maritime Students: 245 Marks or GPA of 2.76 out of 4.5 or 3.06 out of 5.0. 
      Scheme Priority Disciplines and Programmes Table for Hotel and Tourism. 
      Programme: Hotel and Tourism, Number of Awards: 70. 
      Qualifying Cut-Off Mark for Non-Rural and Maritime Students: 250 Marks or GPA of 2.81 out of 4.5 or 3.13 out of 5.0. 
      Qualifying Cut-Off Mark for Rural and Maritime Students: 245 Marks or GPA of 2.76 out of 4.5 or 3.06 out of 5.0. 
      Eligible Institutions: Corpus Christi Teachers College, Fiji National University, Fulton Adventist University College, Sangam Institute of Technology, The University of the South Pacific, University of Fiji.
    `.trim();
  } else if (pageIndex === 50) { // Page 51: Table for Tuition Only Study Loan for In-Service Scheme
    fullText = `
      Tuition Only Study Loan for In-Service Scheme Table. 
      Discipline: Engineering Services, Quota: 10. 
      Discipline: Medicine and Health Services, Quota: 10. 
      Discipline: Agriculture, Forestry and Fisheries, Quota: 5. 
      Discipline: Information Technology, Quota: 5. 
      Discipline: Social Works, Quota: 5. 
      Discipline: Science and Innovation, Quota: 10. 
      Discipline: Land/Town Planning, Quota: 5. 
      Discipline: Business and Commerce, Quota: 20. 
      Discipline: Education, Quota: 20. 
      Discipline: Arts, Quota: 5. 
      Discipline: Hotel and Tourism, Quota: 5. 
      Total Quota: 100. 
      Eligible Institutions: Corpus Christi Teachers College, Fiji National University, Fulton Adventist University College, Sangam Institute of Technology, The University of the South Pacific, University of Fiji.
    `.trim();
  }

    // Add image descriptions if any
    if (imageDescriptions.length > 0) {
      const imageText = imageDescriptions
        .filter(img => img.pageIndex === pageIndex)
        .map(img => img.description)
        .join('. ');
      if (imageText) {
        fullText += `. ${imageText}`;
      }
    }

    fullText = fullText.replace(/([.!?])\s+/g, "$1. ");
    console.log("Speaking text:", fullText.substring(0, 100) + (fullText.length > 100 ? "..." : ""));

    utteranceRef.current = speakText(
      fullText,
      options,
      (event) => {
        // Update current word info for real-time tracking
        if (event.charIndex !== undefined && event.charLength !== undefined) {
          const word = fullText.substring(event.charIndex, event.charIndex + (event.charLength || 1));
          setCurrentWordInfo({
            word: word.trim(),
            charIndex: event.charIndex,
            charLength: event.charLength || 1
          });
        }
        // Call the original onBoundary callback
        if (onBoundary) onBoundary(event);
      },
      () => {
        setSpeaking(false);
        setPaused(false);
        setCurrentWordInfo(null);
        if (onEnd) onEnd();
      }
    );
    setSpeaking(true);
    setPaused(false);
  }, [options]);

  const resumeFromText = useCallback((text: string, pageIndex: number) => {
    stop();
    const onBoundary = typeof utteranceRef.current?.onboundary === 'function'
      ? (utteranceRef.current?.onboundary as (event: SpeechSynthesisEvent) => void)
      : undefined;
    const onEnd = typeof utteranceRef.current?.onend === 'function'
      ? (utteranceRef.current?.onend as () => void)
      : undefined;
    speak(text, [], pageIndex, onBoundary, onEnd);
  }, [speak]);

  const pause = useCallback(() => {
    pauseSpeaking();
    setPaused(true);
  }, []);

  const resume = useCallback(() => {
    resumeSpeaking();
    setPaused(false);
  }, []);

  const stop = useCallback(() => {
    stopSpeaking();
    setSpeaking(false);
    setPaused(false);
  }, []);

  const updateOptions = useCallback((newOptions: Partial<VoiceOptions>) => {
    setOptions(prev => ({ ...prev, ...newOptions }));
  }, []);

  useEffect(() => {
    return () => {
      stopSpeaking();
    };
  }, []);

  // Function to speak from a specific position in the text
  const speakFromPosition = useCallback((
    text: string,
    startPosition: number,
    pageIndex: number = 0,
    onBoundary?: (event: SpeechSynthesisEvent) => void,
    onEnd?: () => void
  ) => {
    stop();
    const textToSpeak = text.substring(startPosition);
    speak(textToSpeak, [], pageIndex, onBoundary, onEnd);
  }, [speak, stop]);

  // Function to get current word information
  const getCurrentWordInfo = useCallback(() => {
    return currentWordInfo;
  }, [currentWordInfo]);

  // Function to get current word index
  const getCurrentWordIndex = useCallback(() => {
    return currentWordIndex;
  }, [currentWordIndex]);

  // Function to set word boundary callback
  const setWordBoundaryCallbackFn = useCallback((callback: ((wordIndex: number, word: string) => void) | null) => {
    setWordBoundaryCallback(() => callback);
  }, []);

  return {
    voices,
    options,
    speaking,
    paused,
    speak,
    pause,
    resume,
    stop,
    setOptions: updateOptions,
    resumeFromText,
    speakFromPosition,
    getCurrentWordInfo,
    getCurrentWordIndex,
    setWordBoundaryCallback: setWordBoundaryCallbackFn,
  };
};