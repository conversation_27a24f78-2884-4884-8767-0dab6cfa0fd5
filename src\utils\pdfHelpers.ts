// /**
//  * Helper functions for PDF processing
//  */
// import * as pdfjs from 'pdfjs-dist';
// //import type { PDFDocument } from '../types';
// import type { PDFDocument, ImageDescription } from '../types';
// // Worker is set in PDFViewer.tsx

// /**
//  * Loads a PDF document from a URL or file
//  */
// export const loadPDFDocument = async (source: string | File): Promise<pdfjs.PDFDocumentProxy> => {
//   try {
//     let pdfData: Uint8Array;

//     if (typeof source === 'string') {
//       // If it's a URL, fetch it first
//       if (source.startsWith('http') || source.startsWith('blob:')) {
//         const response = await fetch(source);
//         const arrayBuffer = await response.arrayBuffer();
//         pdfData = new Uint8Array(arrayBuffer);
//       } else {
//         // If it's a local file path, fetch it
//         const response = await fetch(source);
//         const arrayBuffer = await response.arrayBuffer();
//         pdfData = new Uint8Array(arrayBuffer);
//       }
//     } else {
//       // Convert File to array buffer
//       const arrayBuffer = await source.arrayBuffer();
//       pdfData = new Uint8Array(arrayBuffer);
//     }

//     const loadingTask = pdfjs.getDocument({ data: pdfData });
//     return await loadingTask.promise;
//   } catch (error) {
//     console.error('Error loading PDF:', error);
//     throw new Error('Failed to load PDF document');
//   }
// };

// /**
//  * Extracts text from a specific page, preserving reading order (top to bottom, left to right)
//  */
// export const extractTextFromPage = async (
//   pdfDoc: pdfjs.PDFDocumentProxy,
//   pageNumber: number
// ): Promise<string> => {
//   try {
//     const page = await pdfDoc.getPage(pageNumber);
//     const textContent = await page.getTextContent();

//     // Sort items by their vertical position (top to bottom)
//     // For items with similar vertical positions, sort by horizontal position (left to right)
//     const sortedItems = [...textContent.items].sort((a: any, b: any) => {
//       // Get vertical positions (y-coordinates)
//       const yA = a.transform ? a.transform[5] : a.y;
//       const yB = b.transform ? b.transform[5] : b.y;

//       // Define a threshold for considering items to be on the same line
//       const sameLineThreshold = 5; // pixels

//       if (Math.abs(yA - yB) > sameLineThreshold) {
//         // Items are on different lines, sort by y-coordinate (top to bottom)
//         return yB - yA; // Reverse order because PDF coordinates start from bottom
//       } else {
//         // Items are on the same line, sort by x-coordinate (left to right)
//         const xA = a.transform ? a.transform[4] : a.x;
//         const xB = b.transform ? b.transform[4] : b.x;
//         return xA - xB;
//       }
//     });

//     // Group items by lines for better readability
//     let currentLine = 0;
//     let lastY: number | null = null;
//     const lineThreshold = 5; // pixels
//     const lines: string[][] = [[]];

//     sortedItems.forEach((item: any) => {
//       const y = item.transform ? item.transform[5] : item.y;

//       // If this is a new line
//       if (lastY !== null && Math.abs(y - lastY) > lineThreshold) {
//         currentLine++;
//         lines[currentLine] = [];
//       }

//       // Add item to current line
//       if (item.str.trim()) {
//         lines[currentLine].push(item.str);
//       }

//       lastY = y;
//     });

//     // Join items within each line with spaces, and join lines with newlines
//     return lines
//       .map(line => line.join(' '))
//       .filter(line => line.trim()) // Remove empty lines
//       .join('. ');
//   } catch (error) {
//     console.error(`Error extracting text from page ${pageNumber}:`, error);
//     return '';
//   }
// };

// /**
//  * Extracts text from all pages
//  */
// export const extractAllText = async (pdfDoc: pdfjs.PDFDocumentProxy): Promise<string[]> => {
//   const totalPages = pdfDoc.numPages;
//   const textByPages: string[] = [];

//   for (let i = 1; i <= totalPages; i++) {
//     const pageText = await extractTextFromPage(pdfDoc, i);
//     textByPages.push(pageText);
//   }

//   return textByPages;
// };

// export const extractImageDescriptions = async (
//   _pdfDoc: pdfjs.PDFDocumentProxy // Unused but kept for API compatibility
// ): Promise<ImageDescription[]> => {
//   // We'll only include actual image descriptions, not generic ones
//   const descriptions: ImageDescription[] = [];

//   // Add specific image descriptions for pages that we know have images
//   // These would normally come from metadata or alt text in the PDF

//   // Page 5 (index 4) - Images with descriptions
//   descriptions.push({
//     pageIndex: 4,
//     description: "The image shows a diagram related to the document content.",
//     coordinates: { x: 0, y: 0, width: 100, height: 100 }
//   });

//   // You can add more specific image descriptions for other pages as needed

//   return descriptions;
// };

// /**
//  * Creates a PDFDocument object from a loaded PDF
//  */
// export const createPDFDocumentObject = (
//   pdfDoc: pdfjs.PDFDocumentProxy,
//   source: string | File
// ): PDFDocument => {
//   const filename = typeof source === 'string'
//     ? source.split('/').pop() || 'document.pdf'
//     : source.name;

//   return {
//     id: Math.random().toString(36).substring(2, 9),
//     name: filename,
//     url: typeof source === 'string' ? source : URL.createObjectURL(source),
//     totalPages: pdfDoc.numPages,
//     currentPage: 1,
//     dateAdded: new Date()
//   };
// };

// /**
//  * Gets word coordinates for highlighting
//  */
// export const getWordCoordinates = async (
//   pdfDoc: pdfjs.PDFDocumentProxy,
//   pageNumber: number,
//   word: string
// ): Promise<any> => {
//   try {
//     const page = await pdfDoc.getPage(pageNumber);
//     const textContent = await page.getTextContent();

//     // Find the item containing the word
//     const item = textContent.items.find((item: any) =>
//       item.str.includes(word)
//     );

//     if (item && 'transform' in item && 'width' in item && 'height' in item) {
//       return {
//         x: item.transform[4],
//         y: item.transform[5],
//         width: item.width,
//         height: item.height
//       };
//     }

//     return null;
//   } catch (error) {
//     console.error('Error getting word coordinates:', error);
//     return null;
//   }
// };

import * as pdfjs from 'pdfjs-dist';
import type { TextItem, PDFDocument, WordItem } from '../types';

export const extractTextFromPage = async (
  pdfDoc: pdfjs.PDFDocumentProxy,
  pageNumber: number
): Promise<{ fullText: string; textItems: TextItem[]; wordItems: WordItem[] }> => {
  try {
    const page = await pdfDoc.getPage(pageNumber);
    const textContent = await page.getTextContent();

    const textItems: TextItem[] = [];
    const wordItems: WordItem[] = [];
    let fullText = '';
    let currentIndex = 0;

    // Filter and sort text items
    const sortedItems = textContent.items
      .filter((item): item is any => 'str' in item && 'transform' in item)
      .sort((a: any, b: any) => {
        const yA = a.transform[5];
        const yB = b.transform[5];
        const sameLineThreshold = 10;
        if (Math.abs(yA - yB) > sameLineThreshold) {
          return yB - yA; // Top to bottom
        } else {
          const xA = a.transform[4];
          const xB = b.transform[4];
          return xA - xB; // Left to right
        }
      });

    for (let itemIndex = 0; itemIndex < sortedItems.length; itemIndex++) {
      const item = sortedItems[itemIndex];
      const text = item.str.trim();

      if (text) {
        const startIndex = currentIndex;
        const endIndex = currentIndex + text.length;
        const coordinates = {
          x: item.transform[4],
          y: item.transform[5],
          width: item.width,
          height: item.height,
        };

        // Create text item
        textItems.push({
          pageIndex: pageNumber - 1,
          text,
          startIndex,
          endIndex,
          coordinates,
        });

        // Extract words from this text item
        const words = text.split(/\s+/).filter((word: string) => word.length > 0);
        let wordStartIndex = startIndex;

        for (const word of words) {
          const wordEndIndex = wordStartIndex + word.length;

          // Calculate approximate word coordinates
          const wordWidth = (coordinates.width / text.length) * word.length;
          const wordX = coordinates.x + ((wordStartIndex - startIndex) / text.length) * coordinates.width;

          wordItems.push({
            pageIndex: pageNumber - 1,
            word,
            startIndex: wordStartIndex,
            endIndex: wordEndIndex,
            coordinates: {
              x: wordX,
              y: coordinates.y,
              width: wordWidth,
              height: coordinates.height,
            },
            textItemIndex: itemIndex,
          });

          // Move to next word (account for space)
          wordStartIndex = wordEndIndex + 1;
        }

        fullText += text + ' ';
        currentIndex = endIndex + 1;
      }
    }

    return { fullText: fullText.trim(), textItems, wordItems };
  } catch (error) {
    console.error(`Error extracting text from page ${pageNumber}:`, error);
    return { fullText: '', textItems: [], wordItems: [] };
  }
};

export const createPDFDocumentObject = (
  pdfDoc: pdfjs.PDFDocumentProxy,
  source: string | File
): PDFDocument => {
  const filename = typeof source === 'string'
    ? source.split('/').pop() || 'document.pdf'
    : source.name;

  return {
    id: Math.random().toString(36).substring(2, 9),
    name: filename,
    url: typeof source === 'string' ? source : URL.createObjectURL(source),
    totalPages: pdfDoc.numPages,
    currentPage: 1,
    dateAdded: new Date(),
    pageHeight: undefined, // Will be set in usePDFDocument
  };
};