import type { VoiceOptions } from '../types';

/**
 * Speech synthesis utility functions
 */

// Get available voices
export const getAvailableVoices = (): SpeechSynthesisVoice[] => {
  return window.speechSynthesis.getVoices();
};

// Initialize default voice options
export const initVoiceOptions = (): VoiceOptions => {
  return {
    rate: 1,
    pitch: 1,
    volume: 1,
    voice: null
  };
};

// Speak text with enhanced boundary detection
export const speakText = (
  text: string,
  options: VoiceOptions,
  onBoundary?: (event: SpeechSynthesisEvent) => void,
  onEnd?: () => void,
  onStart?: () => void,
  onError?: (event: SpeechSynthesisErrorEvent) => void
): SpeechSynthesisUtterance => {
  const utterance = new SpeechSynthesisUtterance(text);

  // Set voice options
  utterance.rate = options.rate;
  utterance.pitch = options.pitch;
  utterance.volume = options.volume;

  // Set voice preference to UK English Female if available
  const voices = window.speechSynthesis.getVoices();
  const preferredVoice = voices.find(voice =>
    voice.lang.includes('en-GB') && voice.name.toLowerCase().includes('female')
  ) || voices.find(voice =>
    voice.lang.includes('en-GB')
  ) || voices.find(voice =>
    voice.lang.includes('en-US') && voice.name.toLowerCase().includes('female')
  ) || options.voice;

  if (preferredVoice) {
    utterance.voice = preferredVoice;
  }

  // Add event listeners with enhanced boundary detection
  if (onBoundary) {
    utterance.onboundary = (event) => {
      // Enhanced boundary event with better word detection
      let charLength = event.charLength || 1;

      // Try to detect word boundaries more accurately
      if (event.name === 'word' && event.charIndex !== undefined) {
        const remainingText = text.substring(event.charIndex);
        const wordMatch = remainingText.match(/^\S+/);
        if (wordMatch) {
          charLength = wordMatch[0].length;
        }
      }

      const boundaryEvent = {
        ...event,
        charIndex: event.charIndex,
        charLength: charLength,
        name: event.name,
        elapsedTime: event.elapsedTime
      };
      onBoundary(boundaryEvent);
    };
  }

  if (onStart) {
    utterance.onstart = onStart;
  }

  if (onEnd) {
    utterance.onend = onEnd;
  }

  if (onError) {
    utterance.onerror = onError;
  }

  // Add pause and resume handlers
  utterance.onpause = () => {
    console.log('Speech paused');
  };

  utterance.onresume = () => {
    console.log('Speech resumed');
  };

  // Speak
  window.speechSynthesis.speak(utterance);

  return utterance;
};

// Pause speaking
export const pauseSpeaking = (): void => {
  window.speechSynthesis.pause();
};

// Resume speaking
export const resumeSpeaking = (): void => {
  window.speechSynthesis.resume();
};

// Stop speaking
export const stopSpeaking = (): void => {
  window.speechSynthesis.cancel();
};

// Check if speaking
export const isSpeaking = (): boolean => {
  return window.speechSynthesis.speaking;
};

// Check if paused
export const isPaused = (): boolean => {
  return window.speechSynthesis.paused;
};

// Update voice options
export const updateVoiceOptions = (
  currentOptions: VoiceOptions, 
  newOptions: Partial<VoiceOptions>
): VoiceOptions => {
  return {
    ...currentOptions,
    ...newOptions
  };
};